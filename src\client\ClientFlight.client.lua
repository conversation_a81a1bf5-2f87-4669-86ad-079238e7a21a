-- ClientFlight
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = game.Players.LocalPlayer
local char = player.Character or player.CharacterAdded:Wait()
local root = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("HumanoidRootPart")
local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid")
local remote = ReplicatedStorage:WaitForChild("SupermanShared"):WaitForChild("FlightRemote")

------------------------------------------------------------------
-- BODY MOVERS
------------------------------------------------------------------
local gyro = Instance.new("BodyGyro")
gyro.MaxTorque = Vector3.new(400000, 400000, 400000)
gyro.P = 10000
gyro.Parent = root

local vel = Instance.new("BodyVelocity")
vel.MaxForce = Vector3.new(400000, 400000, 400000)
vel.Parent = root

------------------------------------------------------------------
-- INPUT
------------------------------------------------------------------
local flying = false
local w, a, s, d, q, e = false, false, false, false, false, false

local function updateInputs(input, state)
    local key = input.KeyCode
    if key == Enum.KeyCode.W then w = state end
    if key == Enum.KeyCode.A then a = state end
    if key == Enum.KeyCode.S then s = state end
    if key == Enum.KeyCode.D then d = state end
    if key == Enum.KeyCode.Space then q = state end
    if key == Enum.KeyCode.LeftShift then e = state end
end

UserInputService.InputBegan:Connect(function(input) updateInputs(input, true) end)
UserInputService.InputEnded:Connect(function(input) updateInputs(input, false) end)

------------------------------------------------------------------
-- LOOP
------------------------------------------------------------------
RunService.RenderStepped:Connect(function(dt)
    if not flying then
        if humanoid:GetState() == Enum.HumanoidStateType.Freefall or root.Velocity.Y < -50 then
            flying = true
            humanoid.PlatformStand = true
        end
    else
        local cf = workspace.CurrentCamera.CFrame
        local moveDir = Vector3.new(
            (d and 1 or 0) + (a and -1 or 0),
            (q and 1 or 0) + (e and -1 or 0),
            (s and 1 or 0) + (w and -1 or 0)
        )
        moveDir = cf:VectorToWorldSpace(moveDir)
        vel.Velocity = moveDir * 80   -- client side visual speed (server caps)
        gyro.CFrame = cf
    end
end)
