-- ClientFlight - Enhanced 10x Version
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")
local SoundService = game:GetService("SoundService")

local player = game.Players.LocalPlayer
local char = player.Character or player.CharacterAdded:Wait()
local root = char:Wait<PERSON><PERSON><PERSON>hil<PERSON>("HumanoidRootPart")
local humanoid = char:Wait<PERSON><PERSON><PERSON>hild("Humanoid")
local remote = ReplicatedStorage:WaitFor<PERSON>hild("SupermanShared"):Wait<PERSON><PERSON><PERSON>hild("FlightRemote")

------------------------------------------------------------------
-- ENHANCED FLIGHT SETTINGS
------------------------------------------------------------------
local FLIGHT_CONFIG = {
    BASE_SPEED = 120,
    BOOST_SPEED = 300,
    ACCELERATION = 8,
    DECELERATION = 12,
    TURN_SPEED = 15,
    HOVER_FORCE = 0.3,
    SMOOTHING = 0.85,
    AUTO_HOVER_HEIGHT = 50
}

------------------------------------------------------------------
-- ENHANCED BODY MOVERS
------------------------------------------------------------------
local gyro = Instance.new("BodyAngularVelocity")
gyro.MaxTorque = Vector3.new(math.huge, math.huge, math.huge)
gyro.AngularVelocity = Vector3.new(0, 0, 0)
gyro.Parent = root

local vel = Instance.new("BodyVelocity")
vel.MaxForce = Vector3.new(math.huge, math.huge, math.huge)
vel.Velocity = Vector3.new(0, 0, 0)
vel.Parent = root

------------------------------------------------------------------
-- FLIGHT STATE
------------------------------------------------------------------
local flying = false
local boosting = false
local hovering = false
local currentSpeed = 0
local targetSpeed = 0
local currentVelocity = Vector3.new(0, 0, 0)
local targetVelocity = Vector3.new(0, 0, 0)

------------------------------------------------------------------
-- INPUT TRACKING
------------------------------------------------------------------
local keys = {
    w = false, a = false, s = false, d = false,
    space = false, shift = false, ctrl = false,
    f = false -- Toggle flight
}

local function updateInputs(input, state)
    local key = input.KeyCode
    if key == Enum.KeyCode.W then keys.w = state end
    if key == Enum.KeyCode.A then keys.a = state end
    if key == Enum.KeyCode.S then keys.s = state end
    if key == Enum.KeyCode.D then keys.d = state end
    if key == Enum.KeyCode.Space then keys.space = state end
    if key == Enum.KeyCode.LeftShift then keys.shift = state end
    if key == Enum.KeyCode.LeftControl then keys.ctrl = state end
    if key == Enum.KeyCode.F and state then
        -- Toggle flight mode
        if flying then
            stopFlight()
        else
            startFlight()
        end
    end
end

------------------------------------------------------------------
-- FLIGHT CONTROL FUNCTIONS
------------------------------------------------------------------
function startFlight()
    flying = true
    humanoid.PlatformStand = true
    humanoid:ChangeState(Enum.HumanoidStateType.Physics)

    -- Smooth entry into flight
    local entryTween = TweenService:Create(vel,
        TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {Velocity = Vector3.new(0, 20, 0)}
    )
    entryTween:Play()
end

function stopFlight()
    flying = false
    boosting = false
    hovering = false
    humanoid.PlatformStand = false

    -- Smooth landing
    local landingTween = TweenService:Create(vel,
        TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {Velocity = Vector3.new(0, -20, 0)}
    )
    landingTween:Play()

    wait(1)
    if not flying then
        vel.Velocity = Vector3.new(0, 0, 0)
    end
end

------------------------------------------------------------------
-- ENHANCED MOVEMENT CALCULATION
------------------------------------------------------------------
function calculateMovement(dt)
    local camera = workspace.CurrentCamera
    local cameraCF = camera.CFrame

    -- Calculate input direction
    local moveVector = Vector3.new(
        (keys.d and 1 or 0) - (keys.a and 1 or 0),
        (keys.space and 1 or 0) - (keys.shift and 1 or 0),
        (keys.s and 1 or 0) - (keys.w and 1 or 0)
    )

    -- Transform to world space
    local worldMoveVector = cameraCF:VectorToWorldSpace(moveVector)

    -- Determine target speed
    boosting = keys.ctrl
    local maxSpeed = boosting and FLIGHT_CONFIG.BOOST_SPEED or FLIGHT_CONFIG.BASE_SPEED

    -- Auto-hover when no vertical input
    if moveVector.Y == 0 and flying then
        hovering = true
        local groundRay = workspace:Raycast(root.Position, Vector3.new(0, -1000, 0))
        if groundRay and groundRay.Distance < FLIGHT_CONFIG.AUTO_HOVER_HEIGHT then
            worldMoveVector = worldMoveVector + Vector3.new(0, FLIGHT_CONFIG.HOVER_FORCE, 0)
        end
    else
        hovering = false
    end

    -- Calculate target velocity
    if moveVector.Magnitude > 0 then
        targetSpeed = maxSpeed
        targetVelocity = worldMoveVector.Unit * targetSpeed
    else
        targetSpeed = 0
        targetVelocity = Vector3.new(0, hovering and 5 or 0, 0)
    end

    -- Smooth acceleration/deceleration
    local accel = targetSpeed > currentSpeed and FLIGHT_CONFIG.ACCELERATION or FLIGHT_CONFIG.DECELERATION
    currentSpeed = currentSpeed + (targetSpeed - currentSpeed) * accel * dt

    -- Smooth velocity interpolation
    currentVelocity = currentVelocity:Lerp(targetVelocity, FLIGHT_CONFIG.SMOOTHING * dt * 60)

    return currentVelocity, cameraCF
end

------------------------------------------------------------------
-- INPUT CONNECTIONS
------------------------------------------------------------------
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    updateInputs(input, true)
end)

UserInputService.InputEnded:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    updateInputs(input, false)
end)

------------------------------------------------------------------
-- AUTO FLIGHT TRIGGER
------------------------------------------------------------------
local function checkAutoFlight()
    if not flying then
        -- Auto-start flight when falling fast or jumping
        if humanoid:GetState() == Enum.HumanoidStateType.Freefall or
           root.Velocity.Y < -30 or
           (humanoid:GetState() == Enum.HumanoidStateType.Jumping and keys.space) then
            startFlight()
        end
    end
end

------------------------------------------------------------------
-- ENHANCED MAIN LOOP
------------------------------------------------------------------
local lastUpdate = tick()
RunService.Heartbeat:Connect(function()
    local currentTime = tick()
    local dt = currentTime - lastUpdate
    lastUpdate = currentTime

    -- Check for auto-flight
    checkAutoFlight()

    if flying then
        local newVelocity, cameraCF = calculateMovement(dt)

        -- Apply velocity
        vel.Velocity = newVelocity

        -- Enhanced rotation - smooth camera following
        if newVelocity.Magnitude > 5 then
            local lookDirection = newVelocity.Unit
            local targetCF = CFrame.lookAt(root.Position, root.Position + lookDirection)

            -- Smooth rotation
            gyro.AngularVelocity = Vector3.new(0, 0, 0)
            local currentCF = root.CFrame
            local lerpedCF = currentCF:Lerp(targetCF, FLIGHT_CONFIG.TURN_SPEED * dt)
            root.CFrame = lerpedCF
        else
            -- When hovering, face camera direction
            local targetCF = CFrame.new(root.Position, root.Position + cameraCF.LookVector)
            root.CFrame = root.CFrame:Lerp(targetCF, FLIGHT_CONFIG.TURN_SPEED * dt * 0.5)
        end

        -- Visual effects based on speed
        if boosting and currentSpeed > FLIGHT_CONFIG.BASE_SPEED then
            -- Add boost effects here (particles, sounds, etc.)
        end
    else
        -- Ensure body movers are reset when not flying
        vel.Velocity = Vector3.new(0, 0, 0)
        gyro.AngularVelocity = Vector3.new(0, 0, 0)
    end
end)

------------------------------------------------------------------
-- CHARACTER RESPAWN HANDLING
------------------------------------------------------------------
player.CharacterAdded:Connect(function(newChar)
    char = newChar
    root = char:WaitForChild("HumanoidRootPart")
    humanoid = char:WaitForChild("Humanoid")

    -- Recreate body movers
    if gyro then gyro:Destroy() end
    if vel then vel:Destroy() end

    gyro = Instance.new("BodyAngularVelocity")
    gyro.MaxTorque = Vector3.new(math.huge, math.huge, math.huge)
    gyro.AngularVelocity = Vector3.new(0, 0, 0)
    gyro.Parent = root

    vel = Instance.new("BodyVelocity")
    vel.MaxForce = Vector3.new(math.huge, math.huge, math.huge)
    vel.Velocity = Vector3.new(0, 0, 0)
    vel.Parent = root

    -- Reset flight state
    flying = false
    boosting = false
    hovering = false
    currentSpeed = 0
    targetSpeed = 0
    currentVelocity = Vector3.new(0, 0, 0)
    targetVelocity = Vector3.new(0, 0, 0)
end)
