-- ClientFlight
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = game.Players.LocalPlayer
local char = player.Character or player.CharacterAdded:Wait()
local root = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("HumanoidRootPart")
local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid")
local remote = ReplicatedStorage:WaitForChild("SupermanShared"):WaitForChild("FlightRemote")

------------------------------------------------------------------
-- BODY MOVERS
------------------------------------------------------------------
local gyro = Instance.new("BodyGyro")
gyro.MaxTorque = Vector3.new(400000, 400000, 400000)
gyro.P = 10000
gyro.Parent = root

local vel = Instance.new("BodyVelocity")
vel.MaxForce = Vector3.new(400000, 400000, 400000)
vel.Parent = root

------------------------------------------------------------------
-- INPUT
------------------------------------------------------------------
local flying = false
local w, a, s, d, q, e = false, false, false, false, false, false

local function updateInputs(input, state)
    local key = input.KeyCode
    if key == Enum.KeyCode.W then w = state end
    if key == Enum.KeyCode.A then a = state end
    if key == Enum.KeyCode.S then s = state end
    if key == Enum.KeyCode.D then d = state end
    if key == Enum.KeyCode.Space then q = state end
    if key == Enum.KeyCode.LeftShift then e = state end
end

UserInputService.InputBegan:Connect(function(input) updateInputs(input, true) end)
UserInputService.InputEnded:Connect(function(input) updateInputs(input, false) end)

------------------------------------------------------------------
-- LOOP
------------------------------------------------------------------
RunService.RenderStepped:Connect(function(dt)
    if not flying then
        if humanoid:GetState() == Enum.HumanoidStateType.Freefall or root.Velocity.Y < -50 then
            flying = true
            humanoid.PlatformStand = true
        end
    else
        local cf = workspace.CurrentCamera.CFrame
        local moveDir = Vector3.new(
            (d and 1 or 0) + (a and -1 or 0),
            (q and 1 or 0) + (e and -1 or 0),
            (s and 1 or 0) + (w and -1 or 0)
        )
        moveDir = cf:VectorToWorldSpace(moveDir)
        vel.Velocity = moveDir * 80   -- client side visual speed (server caps)
        gyro.CFrame = cf
    end
end)





------------------------------------------------------------------
-- HEAT VISION SYSTEM
------------------------------------------------------------------
local heatVisionActive = false
local heatVisionBeams = {}

-- Create heat vision beams from eyes to target
local function createHeatVisionBeams(targetPos)
    -- Remove old beams
    for _, beam in pairs(heatVisionBeams) do
        if beam and beam.Parent then
            beam:Destroy()
        end
    end
    heatVisionBeams = {}

    if not char or not char:FindFirstChild("Head") then return end

    -- Get eye positions
    local head = char.Head
    local headCFrame = head.CFrame
    local eyeOffset = 0.15
    local rightEye = headCFrame * CFrame.new(eyeOffset, 0.1, -0.5)
    local leftEye = headCFrame * CFrame.new(-eyeOffset, 0.1, -0.5)

    -- Create beams from each eye to target
    for i, eyePos in pairs({rightEye.Position, leftEye.Position}) do
        local distance = (targetPos - eyePos).Magnitude
        local beam = Instance.new("Part")
        beam.Name = "HeatVisionBeam"
        beam.Anchored = true
        beam.CanCollide = false
        beam.Size = Vector3.new(0.1, 0.1, distance)
        beam.CFrame = CFrame.new(eyePos, targetPos) * CFrame.new(0, 0, -distance / 2)
        beam.BrickColor = BrickColor.new("Bright red")
        beam.Material = Enum.Material.Neon
        beam.Transparency = 0.2
        beam.Parent = workspace

        table.insert(heatVisionBeams, beam)
    end
end

-- Update heat vision continuously
RunService.Heartbeat:Connect(function()
    if heatVisionActive then
        -- Get mouse position in world
        local mouse = player:GetMouse()
        local camera = workspace.CurrentCamera

        local rayOrigin = camera.CFrame.Position
        local rayDirection = (mouse.Hit.Position - rayOrigin).Unit * 500

        local raycastParams = RaycastParams.new()
        raycastParams.FilterDescendantsInstances = {char}
        raycastParams.FilterType = Enum.RaycastFilterType.Blacklist

        local raycastResult = workspace:Raycast(rayOrigin, rayDirection, raycastParams)
        local hitPos = raycastResult and raycastResult.Position or mouse.Hit.Position

        -- Create beams to hit position
        createHeatVisionBeams(hitPos)

        -- Fire server event for damage
        powerRemote:FireServer("HeatVision", hitPos)
    end
end)

------------------------------------------------------------------
-- HEAT VISION INPUT
------------------------------------------------------------------
-- Mouse/touch input for heat vision
UserInputService.InputBegan:Connect(function(input, gpe)
    if gpe then return end

    if input.UserInputType == Enum.UserInputType.MouseButton1 or
       input.UserInputType == Enum.UserInputType.Touch then
        heatVisionActive = true
        print("🔥 Heat vision activated!")
    end
end)

UserInputService.InputEnded:Connect(function(input, gpe)
    if input.UserInputType == Enum.UserInputType.MouseButton1 or
       input.UserInputType == Enum.UserInputType.Touch then
        heatVisionActive = false

        -- Clean up beams
        for _, beam in pairs(heatVisionBeams) do
            if beam and beam.Parent then
                beam:Destroy()
            end
        end
        heatVisionBeams = {}
        print("🔥 Heat vision deactivated!")
    end
end)
