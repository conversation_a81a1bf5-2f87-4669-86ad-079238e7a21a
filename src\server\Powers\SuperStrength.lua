-- SuperStrength module (server)
local Config = require(game.ReplicatedStorage.SupermanShared.Config)

return function(player, mouseHit)
    local char = player.Character
    if not char then return end
    local root = char:Find<PERSON>irs<PERSON><PERSON>hild("HumanoidRootPart")
    local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid")
    if not root or humanoid.Health <= 0 then return end

    -- Find target in melee range
    local meleeRange = 10 -- studs
    local closestTarget = nil
    local closestDistance = math.huge
    
    for _, otherPlayer in pairs(game.Players:GetPlayers()) do
        if otherPlayer ~= player and otherPlayer.Character then
            local otherRoot = otherPlayer.Character:FindFirstChild("HumanoidRootPart")
            local otherHumanoid = otherPlayer.Character:FindFirstChild("Humanoid")
            
            if otherRoot and otherHumanoid and otherHumanoid.Health > 0 then
                local distance = (otherRoot.Position - root.Position).Magnitude
                if distance <= meleeRange and distance < closestDistance then
                    closestTarget = otherPlayer.Character
                    closestDistance = distance
                end
            end
        end
    end
    
    -- Deal damage and knockback
    if closestTarget then
        local targetHumanoid = closestTarget:Find<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Humanoid")
        local targetRoot = closestTarget:Find<PERSON><PERSON><PERSON><PERSON>hild("HumanoidRootPart")
        
        if targetHumanoid and targetRoot then
            -- Damage
            targetHumanoid:TakeDamage(Config.POWERS.SuperStrength.dmg)
            
            -- Knockback
            local knockbackDirection = (targetRoot.Position - root.Position).Unit
            local bodyVelocity = Instance.new("BodyVelocity")
            bodyVelocity.MaxForce = Vector3.new(4000, 0, 4000)
            bodyVelocity.Velocity = knockbackDirection * 50
            bodyVelocity.Parent = targetRoot
            
            -- Remove knockback after short time
            game:GetService("Debris"):AddItem(bodyVelocity, 0.5)
        end
    end

    -- Visual effect
    local remote = game.ReplicatedStorage.SupermanShared.PowerRemote
    remote:FireAllClients("SuperStrength", root.Position, mouseHit)
end
