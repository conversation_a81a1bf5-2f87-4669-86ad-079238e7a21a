{"name": "<PERSON><PERSON><PERSON><PERSON>", "tree": {"$className": "DataModel", "ReplicatedStorage": {"$className": "ReplicatedStorage", "SupermanShared": {"$className": "Folder", "FlightRemote": {"$className": "RemoteEvent"}, "PowerRemote": {"$className": "RemoteEvent"}, "Config": {"$path": "src/shared/Config.lua"}}}, "ServerScriptService": {"$className": "ServerScriptService", "SupermanMain": {"$path": "src/server/SupermanMain.server.lua"}}, "StarterPlayer": {"$className": "StarterPlayer", "StarterPlayerScripts": {"$className": "StarterPlayerScripts", "ClientFlight": {"$path": "src/client/ClientFlight.client.lua"}, "PowerControls": {"$path": "src/client/PowerControls.client.lua"}}}, "ServerStorage": {"$className": "ServerStorage", "Powers": {"$className": "Folder", "HeatVision": {"$path": "src/server/Powers/HeatVision.lua"}, "FreezeBreath": {"$path": "src/server/Powers/FreezeBreath.lua"}, "SuperStrength": {"$path": "src/server/Powers/SuperStrength.lua"}}}}}