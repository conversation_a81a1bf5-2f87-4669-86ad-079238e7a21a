-- PowerControls - Client-side power input handling
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Wait for shared folder and remote
local shared = ReplicatedStorage:WaitFor<PERSON>hild("SupermanShared")
local PowerRemote = shared:WaitForChild("PowerRemote")
local Config = require(shared:WaitForChild("Config"))

-- Power cooldowns (client-side tracking)
local lastPowerUse = {}

------------------------------------------------------------------
-- POWER ACTIVATION FUNCTION
------------------------------------------------------------------
local function activatePower(powerName)
    local currentTime = tick()
    local powerConfig = Config.POWERS[powerName]
    
    if not powerConfig then
        warn("Power not found in config:", powerName)
        return
    end
    
    -- Check cooldown
    if lastPowerUse[powerName] and currentTime - lastPowerUse[powerName] < powerConfig.cd then
        print("⏰ Power on cooldown:", powerName, "wait", powerConfig.cd - (currentTime - lastPowerUse[powerName]), "seconds")
        return
    end
    
    -- Update last use time
    lastPowerUse[powerName] = currentTime
    
    -- Get mouse hit position
    local mouseHit = mouse.Hit.Position
    
    -- Fire to server
    PowerRemote:FireServer(powerName, mouseHit)
    print("🔥 Activated power:", powerName, "at position:", mouseHit)
end

------------------------------------------------------------------
-- INPUT HANDLING
------------------------------------------------------------------
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    local keyCode = input.KeyCode
    
    -- Power keybinds
    if keyCode == Enum.KeyCode.Q then
        activatePower("HeatVision")
    elseif keyCode == Enum.KeyCode.E then
        activatePower("FreezeBreath")
    elseif keyCode == Enum.KeyCode.R then
        activatePower("SuperStrength")
    end
end)

------------------------------------------------------------------
-- VISUAL EFFECTS (CLIENT-SIDE)
------------------------------------------------------------------
PowerRemote.OnClientEvent:Connect(function(powerName, startPos, endPos)
    print("🎨 Received visual effect:", powerName, "from", startPos, "to", endPos)
    
    if powerName == "HeatVision" then
        -- Create heat vision beam effect
        local beam = Instance.new("Part")
        beam.Name = "HeatVisionBeam"
        beam.Anchored = true
        beam.CanCollide = false
        beam.Material = Enum.Material.Neon
        beam.BrickColor = BrickColor.new("Really red")
        beam.Shape = Enum.PartType.Cylinder
        
        -- Position and size the beam
        local distance = (endPos - startPos).Magnitude
        beam.Size = Vector3.new(distance, 0.5, 0.5)
        beam.CFrame = CFrame.lookAt(startPos, endPos) * CFrame.new(0, 0, -distance/2)
        beam.Parent = workspace
        
        -- Fade out effect
        local tween = game:GetService("TweenService"):Create(beam,
            TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            {Transparency = 1, Size = Vector3.new(distance, 0.1, 0.1)}
        )
        tween:Play()
        
        -- Clean up
        game:GetService("Debris"):AddItem(beam, 0.5)
        
    elseif powerName == "FreezeBreath" then
        -- Create freeze breath effect (placeholder)
        print("❄️ Freeze breath visual effect at", endPos)
        
    elseif powerName == "SuperStrength" then
        -- Create super strength effect (placeholder)
        print("💪 Super strength visual effect at", endPos)
    end
end)

print("🎮 Power Controls loaded! Keybinds:")
print("Q - Heat Vision")
print("E - Freeze Breath") 
print("R - Super Strength")
