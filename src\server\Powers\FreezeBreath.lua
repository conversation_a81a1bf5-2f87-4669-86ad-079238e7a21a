-- FreezeBreath module (server)
local Config = require(game.ReplicatedStorage.SupermanShared.Config)

return function(player, mouseHit)
    local char = player.Character
    if not char then return end
    local root = char:Find<PERSON><PERSON><PERSON><PERSON>hild("HumanoidRootPart")
    local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid")
    if not root or humanoid.Health <= 0 then return end

    -- Create freeze effect in direction of mouseHit
    local direction = (mouseHit - root.Position).Unit
    local freezeRange = 50 -- studs
    
    -- Find all characters in range and freeze them
    for _, otherPlayer in pairs(game.Players:GetPlayers()) do
        if otherPlayer ~= player and otherPlayer.Character then
            local otherRoot = otherPlayer.Character:FindFirstChild("HumanoidRootPart")
            local otherHumanoid = otherPlayer.Character:FindFirstChild("Humanoid")
            
            if otherRoot and otherHumanoid then
                local distance = (otherRoot.Position - root.Position).Magnitude
                local toTarget = (otherRoot.Position - root.Position).Unit
                local dotProduct = direction:Dot(toTarget)
                
                -- Check if target is in cone and range
                if distance <= freezeRange and dotProduct > 0.7 then -- ~45 degree cone
                    -- Freeze the target
                    otherHumanoid.WalkSpeed = 0
                    otherHumanoid.JumpPower = 0
                    
                    -- Unfreeze after duration
                    task.wait(Config.POWERS.FreezeBreath.duration)
                    if otherHumanoid then
                        otherHumanoid.WalkSpeed = 16
                        otherHumanoid.JumpPower = 50
                    end
                end
            end
        end
    end

    -- Visual effect
    local remote = game.ReplicatedStorage.SupermanShared.PowerRemote
    remote:FireAllClients("FreezeBreath", root.Position, mouseHit)
end
